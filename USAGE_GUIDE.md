# Gemini MCP Chatbot - Usage Guide

## Quick Start (हिंदी में भी)

यह एक powerful CLI chatbot है जो Google Gemini AI को Model Context Protocol (MCP) के साथ जोड़ता है।

### Step 1: Installation

```bash
# Dependencies install करें
npm install

# Setup wizard चलाएं
npm run setup
```

### Step 2: Configuration

Setup wizard आपसे पूछेगा:

1. **Gemini API Key** - [Google AI Studio](https://aistudio.google.com/app/apikey) से मुफ्त में मिलता है
2. **MCP Servers** - कौन से tools चाहिए:
   - Simple: Basic tools (fetch, memory)
   - Full: सभी available servers
   - Custom: अपना configuration

### Step 3: Test Setup

```bash
# Configuration test करें
npm test
```

### Step 4: Start Chatting

```bash
# Chatbot start करें
npm start
```

## Example Conversations

### Basic Chat
```
You: Hello! How are you?
Assistant: Hello! I'm doing great, thank you for asking. I'm ready to help you with various tasks using the tools available to me. How can I assist you today?
```

### With Web Search (if Brave Search configured)
```
You: What's the latest news about artificial intelligence?
Assistant: [Searches the web and provides current AI news with sources]
```

### With File Operations (if filesystem server configured)
```
You: List the files in my current directory
Assistant: [Shows files and directories with details]
```

### With Memory (if memory server configured)
```
You: Remember that my favorite programming language is JavaScript
Assistant: I'll remember that your favorite programming language is JavaScript.

You: What's my favorite programming language?
Assistant: Your favorite programming language is JavaScript.
```

## Available MCP Servers

### Pre-configured Servers (in mcp.json):

1. **brave-search** - Web search capabilities
2. **filesystem** - File and directory operations
3. **github** - GitHub repository access
4. **sqlite/postgres** - Database operations
5. **puppeteer** - Web scraping and automation
6. **fetch** - HTTP requests and API calls
7. **memory** - Persistent memory across conversations
8. **gdrive/gmail** - Google services integration
9. **slack** - Slack workspace integration
10. **notion** - Notion workspace access
11. **obsidian** - Obsidian vault operations
12. **git** - Git repository management
13. **docker/kubernetes** - Container management
14. **aws-kb** - AWS Knowledge Base
15. **azure-ai-search** - Azure AI Search

### How to Enable Servers:

1. Edit `mcp.json` - uncomment the servers you want
2. Add required API keys to `.env` file
3. Restart the chatbot

## Common Use Cases

### 1. Development Assistant
```
You: Create a new React component for a login form
Assistant: [Uses filesystem to create files and provides code]
```

### 2. Research Assistant
```
You: Research the latest trends in machine learning and save the findings
Assistant: [Uses web search + memory to research and store information]
```

### 3. Project Management
```
You: Check my GitHub repositories and create a summary
Assistant: [Uses GitHub API to fetch repos and provide analysis]
```

### 4. Data Analysis
```
You: Connect to my database and show me user statistics
Assistant: [Uses database server to query and analyze data]
```

## Troubleshooting

### Common Issues:

1. **"GEMINI_API_KEY not found"**
   - Run `npm run setup` again
   - Check `.env` file exists and has the key

2. **MCP server connection failed**
   - Check API keys in `.env`
   - Verify server configuration in `mcp.json`
   - Some servers need additional setup (see their documentation)

3. **Tool execution failed**
   - Check server logs for specific errors
   - Verify permissions and API limits
   - Some tools need specific environment setup

### Debug Mode:

For detailed logging, you can modify the chatbot code or check individual MCP server logs.

## Advanced Configuration

### Custom MCP Server:

```json
{
  "mcpServers": {
    "my-custom-server": {
      "command": "node",
      "args": ["path/to/my-server.js"],
      "env": {
        "MY_API_KEY": "value_from_env"
      }
    }
  }
}
```

### Environment Variables:

All API keys should go in `.env`:
```
GEMINI_API_KEY=your_key_here
BRAVE_API_KEY=your_brave_key
GITHUB_PERSONAL_ACCESS_TOKEN=your_github_token
# ... etc
```

## Tips for Best Experience

1. **Start Simple**: Use the simple configuration first, then add more servers
2. **API Keys**: Get free API keys from respective services
3. **Permissions**: Some servers need specific permissions (file access, API scopes)
4. **Rate Limits**: Be aware of API rate limits for external services
5. **Context**: The chatbot remembers conversation context within a session

## Getting API Keys

### Free APIs:
- **Gemini**: [Google AI Studio](https://aistudio.google.com/app/apikey)
- **GitHub**: [Personal Access Tokens](https://github.com/settings/tokens)
- **Brave Search**: [Brave Search API](https://brave.com/search/api/)

### Paid APIs (with free tiers):
- **Google Cloud**: For Drive, Gmail APIs
- **AWS**: For AWS services
- **Azure**: For Azure AI services

## Support

For issues or questions:
1. Check this guide first
2. Review the main README.md
3. Check MCP server documentation
4. Test with `npm test` command

Happy chatting! 🤖✨
