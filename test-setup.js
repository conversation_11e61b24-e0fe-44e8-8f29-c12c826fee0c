#!/usr/bin/env node

import { GoogleGenAI } from '@google/genai';
import fs from 'fs/promises';
import chalk from 'chalk';
import dotenv from 'dotenv';

dotenv.config();

async function testSetup() {
  console.log(chalk.blue.bold('🧪 Testing Gemini MCP Chatbot Setup\n'));
  
  let allGood = true;
  
  // Test 1: Check environment variables
  console.log(chalk.blue('1. Checking environment variables...'));
  
  if (!process.env.GEMINI_API_KEY) {
    console.log(chalk.red('   ❌ GEMINI_API_KEY not found in environment'));
    allGood = false;
  } else {
    console.log(chalk.green('   ✅ GEMINI_API_KEY found'));
  }
  
  // Test 2: Check .env file
  console.log(chalk.blue('\n2. Checking .env file...'));
  
  try {
    await fs.access('.env');
    console.log(chalk.green('   ✅ .env file exists'));
  } catch {
    console.log(chalk.red('   ❌ .env file not found'));
    allGood = false;
  }
  
  // Test 3: Check MCP configuration
  console.log(chalk.blue('\n3. Checking MCP configuration...'));
  
  try {
    const mcpConfig = await fs.readFile('mcp.json', 'utf8');
    const config = JSON.parse(mcpConfig);
    const serverCount = Object.keys(config.mcpServers || {}).length;
    console.log(chalk.green(`   ✅ mcp.json found with ${serverCount} server(s) configured`));
  } catch (error) {
    console.log(chalk.red('   ❌ mcp.json not found or invalid'));
    allGood = false;
  }
  
  // Test 4: Test Gemini API connection
  if (process.env.GEMINI_API_KEY) {
    console.log(chalk.blue('\n4. Testing Gemini API connection...'));
    
    try {
      const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });

      const result = await ai.models.generateContent({
        model: 'gemini-2.0-flash',
        contents: 'Say "Hello, setup test successful!"'
      });
      const response = result.text;
      
      if (response.toLowerCase().includes('hello')) {
        console.log(chalk.green('   ✅ Gemini API connection successful'));
        console.log(chalk.gray(`   Response: ${response}`));
      } else {
        console.log(chalk.yellow('   ⚠️  Gemini API responded but with unexpected content'));
        console.log(chalk.gray(`   Response: ${response}`));
      }
    } catch (error) {
      console.log(chalk.red('   ❌ Gemini API connection failed'));
      console.log(chalk.red(`   Error: ${error.message}`));
      allGood = false;
    }
  }
  
  // Test 5: Check Node.js version
  console.log(chalk.blue('\n5. Checking Node.js version...'));
  
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion >= 18) {
    console.log(chalk.green(`   ✅ Node.js ${nodeVersion} (compatible)`));
  } else {
    console.log(chalk.red(`   ❌ Node.js ${nodeVersion} (requires 18+)`));
    allGood = false;
  }
  
  // Final result
  console.log(chalk.blue('\n' + '='.repeat(50)));
  
  if (allGood) {
    console.log(chalk.green.bold('🎉 All tests passed! Your setup is ready.'));
    console.log(chalk.blue('\nYou can now run: npm start'));
  } else {
    console.log(chalk.red.bold('❌ Some tests failed. Please fix the issues above.'));
    console.log(chalk.blue('\nRun: npm run setup (to reconfigure)'));
  }
  
  console.log(chalk.blue('\n' + '='.repeat(50)));
}

testSetup().catch(error => {
  console.error(chalk.red('Test failed with error:'), error.message);
  process.exit(1);
});
