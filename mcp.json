{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/directory"], "env": {}}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "YOUR_BRAVE_API_KEY_HERE"}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": ""}}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite", "/path/to/database.db"], "env": {}}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"POSTGRES_CONNECTION_STRING": "postgresql://user:password@localhost:5432/dbname"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {}}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "env": {}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}}, "everart": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everart"], "env": {"EVERART_API_KEY": "YOUR_EVERART_API_KEY_HERE"}}, "gdrive": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gdrive"], "env": {"GOOGLE_CLIENT_ID": "YOUR_GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET": "YOUR_GOOGLE_CLIENT_SECRET"}}, "gmail": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gmail"], "env": {"GOOGLE_CLIENT_ID": "YOUR_GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET": "YOUR_GOOGLE_CLIENT_SECRET"}}, "slack": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": "YOUR_SLACK_BOT_TOKEN"}}, "youtube-transcript": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-youtube-transcript"], "env": {}}, "sentry": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sentry"], "env": {"SENTRY_AUTH_TOKEN": "YOUR_SENTRY_AUTH_TOKEN", "SENTRY_ORG_SLUG": "YOUR_SENTRY_ORG_SLUG"}}, "linear": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-linear"], "env": {"LINEAR_API_KEY": "YOUR_LINEAR_API_KEY"}}, "notion": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-notion"], "env": {"NOTION_API_KEY": "YOUR_NOTION_API_KEY"}}, "obsidian": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-obsidian"], "env": {"OBSIDIAN_VAULT_PATH": "/path/to/obsidian/vault"}}, "git": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git"], "env": {}}, "kubernetes": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-kubernetes"], "env": {"KUBECONFIG": "/path/to/kubeconfig"}}, "docker": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-docker"], "env": {}}, "aws-kb": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-aws-kb"], "env": {"AWS_ACCESS_KEY_ID": "YOUR_AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY": "YOUR_AWS_SECRET_ACCESS_KEY", "AWS_REGION": "us-east-1"}}, "azure-ai-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-azure-ai-search"], "env": {"AZURE_AI_SEARCH_ENDPOINT": "YOUR_AZURE_SEARCH_ENDPOINT", "AZURE_AI_SEARCH_KEY": "YOUR_AZURE_SEARCH_KEY"}}}}