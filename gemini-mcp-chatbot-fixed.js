#!/usr/bin/env node

import { GoogleGenAI } from '@google/genai';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import readline from 'readline/promises';
import fs from 'fs/promises';
import path from 'path';
import chalk from 'chalk';
import ora from 'ora';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

class GeminiMCPChatbot {
  constructor() {
    this.geminiApiKey = process.env.GEMINI_API_KEY;
    this.ai = null;
    this.mcpClients = new Map();
    this.availableTools = [];
    
    if (!this.geminiApiKey) {
      console.error(chalk.red('❌ GEMINI_API_KEY environment variable is required'));
      process.exit(1);
    }
    
    this.ai = new GoogleGenAI({ apiKey: this.geminiApiKey });
  }

  async loadMCPConfig() {
    try {
      const configPath = path.join(process.cwd(), 'mcp.json');
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);
      return config.mcpServers || {};
    } catch (error) {
      console.log(chalk.yellow('⚠️  No mcp.json found or invalid format. Running without MCP servers.'));
      return {};
    }
  }

  async connectToMCPServers() {
    const mcpConfig = await this.loadMCPConfig();
    const serverNames = Object.keys(mcpConfig);
    
    if (serverNames.length === 0) {
      console.log(chalk.blue('ℹ️  No MCP servers configured.'));
      return;
    }

    console.log(chalk.blue(`🔌 Connecting to ${serverNames.length} MCP server(s)...`));
    
    for (const [serverName, serverConfig] of Object.entries(mcpConfig)) {
      const spinner = ora(`Connecting to ${serverName}...`).start();
      
      try {
        await this.connectToMCPServer(serverName, serverConfig);
        spinner.succeed(`Connected to ${serverName}`);
      } catch (error) {
        spinner.fail(`Failed to connect to ${serverName}: ${error.message}`);
      }
    }
  }

  async connectToMCPServer(serverName, config) {
    const { command, args = [], env = {} } = config;
    
    // Create MCP client
    const client = new Client({ 
      name: `gemini-mcp-chatbot-${serverName}`, 
      version: '1.0.0' 
    });
    
    // Set up environment variables
    const serverEnv = { ...process.env, ...env };
    
    // Create transport
    const transport = new StdioClientTransport({
      command,
      args,
      env: serverEnv
    });
    
    // Connect to server
    await client.connect(transport);
    
    // Get available tools
    const toolsResult = await client.listTools();
    const tools = toolsResult.tools || [];
    
    // Store client and tools
    this.mcpClients.set(serverName, { client, tools });
    
    // Add tools to available tools list
    tools.forEach(tool => {
      this.availableTools.push({
        serverName,
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema
      });
    });
    
    console.log(chalk.green(`  ✓ ${serverName}: ${tools.length} tool(s) available`));
    tools.forEach(tool => {
      console.log(chalk.gray(`    - ${tool.name}: ${tool.description}`));
    });
  }

  async callMCPTool(toolName, args) {
    // Find which server has this tool
    for (const [serverName, { client, tools }] of this.mcpClients) {
      const tool = tools.find(t => t.name === toolName);
      if (tool) {
        try {
          const result = await client.callTool({
            name: toolName,
            arguments: args
          });
          return result;
        } catch (error) {
          throw new Error(`Tool execution failed on ${serverName}: ${error.message}`);
        }
      }
    }
    throw new Error(`Tool ${toolName} not found in any connected MCP server`);
  }

  formatToolsForGemini() {
    return this.availableTools.map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.inputSchema
    }));
  }

  async processMessage(userMessage) {
    const spinner = ora('🤔 Thinking...').start();
    
    try {
      const tools = this.formatToolsForGemini();
      
      const response = await this.ai.models.generateContent({
        model: 'gemini-2.0-flash',
        contents: userMessage,
        tools: tools.length > 0 ? [{ functionDeclarations: tools }] : undefined
      });

      spinner.stop();

      // Handle function calls
      if (response.functionCalls && response.functionCalls.length > 0) {
        console.log(chalk.blue('🔧 Executing tools...'));
        
        let finalResponse = '';
        
        for (const functionCall of response.functionCalls) {
          const { name, args } = functionCall;
          console.log(chalk.gray(`  → Calling ${name} with args:`, JSON.stringify(args, null, 2)));
          
          try {
            const toolResult = await this.callMCPTool(name, args);
            console.log(chalk.green(`  ✓ ${name} completed`));
            
            // Get follow-up response with tool results
            const followUpResponse = await this.ai.models.generateContent({
              model: 'gemini-2.0-flash',
              contents: `User asked: ${userMessage}\n\nTool ${name} returned: ${JSON.stringify(toolResult.content)}\n\nPlease provide a helpful response based on this information.`
            });
            
            finalResponse = followUpResponse.text;
            
          } catch (error) {
            console.log(chalk.red(`  ✗ ${name} failed: ${error.message}`));
            finalResponse = `I tried to use ${name} but encountered an error: ${error.message}`;
          }
        }
        
        return finalResponse;
      } else {
        return response.text;
      }
    } catch (error) {
      spinner.stop();
      console.error(chalk.red('❌ Error processing message:'), error.message);
      return 'Sorry, I encountered an error processing your request.';
    }
  }

  async startChat() {
    console.log(chalk.green.bold('\n🤖 Gemini MCP Chatbot'));
    console.log(chalk.blue('Type your message and press Enter. Type "exit" to quit.\n'));

    if (this.availableTools.length > 0) {
      console.log(chalk.yellow(`📋 Available tools: ${this.availableTools.map(t => t.name).join(', ')}\n`));
    }

    // Create a new readline interface for each question to avoid issues
    const askQuestion = async (prompt) => {
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });

      try {
        const answer = await rl.question(prompt);
        return answer;
      } finally {
        rl.close();
      }
    };

    while (true) {
      try {
        const userInput = await askQuestion(chalk.cyan('You: '));

        if (userInput.toLowerCase().trim() === 'exit') {
          break;
        }

        if (userInput.trim() === '') {
          continue;
        }

        const response = await this.processMessage(userInput);
        console.log(chalk.green('Assistant:'), response);
        console.log(); // Empty line for readability

      } catch (error) {
        console.error(chalk.red('❌ Error:'), error.message);
      }
    }

    await this.cleanup();
  }

  async cleanup() {
    console.log(chalk.blue('\n👋 Goodbye!'));

    // Close MCP clients
    for (const [serverName, { client }] of this.mcpClients) {
      try {
        await client.close();
      } catch (error) {
        console.error(chalk.red(`Error closing ${serverName}:`, error.message));
      }
    }

    process.exit(0);
  }
}

// Main execution
async function main() {
  const chatbot = new GeminiMCPChatbot();
  
  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    await chatbot.cleanup();
  });
  
  try {
    await chatbot.connectToMCPServers();
    await chatbot.startChat();
  } catch (error) {
    console.error(chalk.red('❌ Fatal error:'), error.message);
    process.exit(1);
  }
}

main().catch(console.error);
