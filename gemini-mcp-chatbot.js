#!/usr/bin/env node

import { GoogleGenerativeAI } from '@google/generative-ai';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import readline from 'readline/promises';
import fs from 'fs/promises';
import path from 'path';

import chalk from 'chalk';
import ora from 'ora';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

class GeminiMCPChatbot {
  constructor() {
    this.geminiApiKey = process.env.GEMINI_API_KEY;
    this.genAI = null;
    this.model = null;
    this.chat = null; // Single persistent chat instance
    this.mcpClients = new Map();
    this.availableTools = [];
    this.rl = null;

    if (!this.geminiApiKey) {
      console.error(chalk.red('❌ GEMINI_API_KEY environment variable is required'));
      process.exit(1);
    }

    this.genAI = new GoogleGenerativeAI(this.geminiApiKey);
  }

  async loadMCPConfig() {
    try {
      const configPath = path.join(process.cwd(), 'mcp.json');
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);
      return config.mcpServers || {};
    } catch (error) {
      console.log(chalk.yellow('⚠️  No mcp.json found or invalid format. Running without MCP servers.'));
      return {};
    }
  }

  async connectToMCPServers() {
    const mcpConfig = await this.loadMCPConfig();
    const serverNames = Object.keys(mcpConfig);
    
    if (serverNames.length === 0) {
      console.log(chalk.blue('ℹ️  No MCP servers configured.'));
      return;
    }

    console.log(chalk.blue(`🔌 Connecting to ${serverNames.length} MCP server(s)...`));
    
    for (const [serverName, serverConfig] of Object.entries(mcpConfig)) {
      const spinner = ora(`Connecting to ${serverName}...`).start();
      
      try {
        await this.connectToMCPServer(serverName, serverConfig);
        spinner.succeed(`Connected to ${serverName}`);
      } catch (error) {
        spinner.fail(`Failed to connect to ${serverName}: ${error.message}`);
      }
    }
  }

  async connectToMCPServer(serverName, config) {
    const { command, args = [], env = {} } = config;
    
    const client = new Client({ 
      name: `gemini-mcp-chatbot-${serverName}`, 
      version: '1.0.0' 
    });
    
    const serverEnv = { ...process.env, ...env };
    
    const transport = new StdioClientTransport({
      command,
      args,
      env: serverEnv
    });
    
    await client.connect(transport);
    
    const toolsResult = await client.listTools();
    const tools = toolsResult.tools || [];
    
    this.mcpClients.set(serverName, { client, tools });
    
    tools.forEach(tool => {
      this.availableTools.push({
        serverName,
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema
      });
    });
    
    console.log(chalk.green(`  ✓ ${serverName}: ${tools.length} tool(s) available`));
    tools.forEach(tool => {
      console.log(chalk.gray(`    - ${tool.name}: ${tool.description}`));
    });
  }

  async callMCPTool(toolName, args) {
    for (const [serverName, { client, tools }] of this.mcpClients) {
      const tool = tools.find(t => t.name === toolName);
      if (tool) {
        try {
          const result = await client.callTool({
            name: toolName,
            arguments: args
          });
          return result;
        } catch (error) {
          throw new Error(`Tool execution failed on ${serverName}: ${error.message}`);
        }
      }
    }
    throw new Error(`Tool ${toolName} not found in any connected MCP server`);
  }

  formatToolsForGemini() {
    return this.availableTools.map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.inputSchema
    }));
  }

  // Initialize the chat session once
  initializeChat() {
    const toolConfigs = {
      functionDeclarations: this.formatToolsForGemini()
    };
    
    this.model = this.genAI.getGenerativeModel({
      model: 'gemini-1.5-flash',
      tools: toolConfigs.functionDeclarations.length > 0 ? [toolConfigs] : undefined
    });

    // Create a single chat session that will persist
    this.chat = this.model.startChat({
      history: []
    });
  }

  async processMessage(userMessage) {
    const spinner = ora('🤔 Thinking...').start();

    try {
      // Send message to the persistent chat instance
      const result = await this.chat.sendMessage(userMessage);
      const response = result.response;
      
      spinner.stop();

      // Check if there are function calls
      const functionCalls = response.functionCalls();
      
      if (functionCalls && functionCalls.length > 0) {
        const call = functionCalls[0];
        console.log(chalk.blue('🔧 Executing tool...'));
        console.log(chalk.gray(`  → Calling ${call.name} with args:`), JSON.stringify(call.args));
        
        try {
          const toolResult = await this.callMCPTool(call.name, call.args);
          
          // Send the tool result back to the same chat instance
          const functionResponseParts = [{
            functionResponse: {
              name: call.name,
              response: {
                content: toolResult.content || JSON.stringify(toolResult) || 'Tool executed successfully'
              }
            }
          }];

          const followUpResult = await this.chat.sendMessage(functionResponseParts);
          const finalResponse = followUpResult.response.text();
          
          console.log(chalk.green(`  ✓ ${call.name} completed`));
          
          return finalResponse;

        } catch (error) {
          console.log(chalk.red(`  ✗ ${call.name} failed: ${error.message}`));
          return `An error occurred while executing the tool: ${error.message}`;
        }

      } else {
        // No function calls, just return the regular response
        const responseText = response.text();
        return responseText;
      }
    } catch (error) {
      spinner.stop();
      console.error(chalk.red('❌ Error processing message:'), error);
      return 'Sorry, I encountered an error processing your request.';
    }
  }

  async startChat() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    console.log(chalk.green.bold('\n🤖 Gemini MCP Chatbot'));
    console.log(chalk.blue('Type your message and press Enter. Type "exit" to quit.\n'));
    
    if (this.availableTools.length > 0) {
      console.log(chalk.yellow(`📋 Available tools: ${this.availableTools.map(t => t.name).join(', ')}\n`));
    }

    // Initialize the chat session once before starting the conversation loop
    this.initializeChat();

    while (true) {
      try {
        const userInput = await this.rl.question(chalk.cyan('You: '));
        
        if (userInput.toLowerCase().trim() === 'exit') {
          break;
        }
        
        if (userInput.trim() === '') {
          continue;
        }

        const response = await this.processMessage(userInput);
        console.log(chalk.green('Assistant:'), response);
        console.log(); // Empty line for readability
        
      } catch (error) {
        console.error(chalk.red('❌ Error:'), error.message);
      }
    }
    
    await this.cleanup();
  }

  async cleanup() {
    console.log(chalk.blue('\n👋 Goodbye!'));
    
    for (const [serverName, { client }] of this.mcpClients) {
      try {
        await client.close();
      } catch (error) {
        console.error(chalk.red(`Error closing ${serverName}:`, error.message));
      }
    }
    
    if (this.rl) {
      this.rl.close();
    }
    
    process.exit(0);
  }
}

// Main execution
async function main() {
  const chatbot = new GeminiMCPChatbot();
  
  process.on('SIGINT', async () => {
    await chatbot.cleanup();
  });
  
  try {
    await chatbot.connectToMCPServers();
    await chatbot.startChat();
  } catch (error) {
    console.error(chalk.red('❌ Fatal error:'), error.message);
    process.exit(1);
  }
}

main().catch(console.error);