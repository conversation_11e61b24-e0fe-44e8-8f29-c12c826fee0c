{"name": "gemini-mcp-chatbot", "version": "1.0.0", "description": "Gemini CLI chatbot with Model Context Protocol (MCP) client support", "main": "gemini-mcp-chatbot.js", "type": "module", "scripts": {"start": "node gemini-mcp-chatbot-fixed.js", "dev": "node --watch gemini-mcp-chatbot-fixed.js", "setup": "node setup.js", "test": "node test-setup.js"}, "keywords": ["gemini", "mcp", "chatbot", "cli", "ai", "model-context-protocol"], "author": "AI Worker", "license": "MIT", "dependencies": {"@google/genai": "^1.0.0", "@google/generative-ai": "^0.24.1", "@modelcontextprotocol/sdk": "^1.0.0", "chalk": "^5.3.0", "dotenv": "^16.4.5", "ora": "^8.0.1", "readline": "^1.3.0"}, "engines": {"node": ">=18.0.0"}}